// styles
import "react-big-calendar/lib/css/react-big-calendar.css";

// styled components
import { <PERSON><PERSON><PERSON>, Header, StyledCalendar, Footer, EmptyState } from "./style";
import { Container as <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@ui/TabNav/style";

// components
import { Calendar, momentLocalizer } from "react-big-calendar";
import Event from "@components/AppointmentsCalendar/Event";
import TimeSlot from "@components/AppointmentsCalendar/TimeSlot";
import DailyToolbar from "@components/AppointmentsCalendar/DailyToolbar";
import DailyNavigation from "@components/AppointmentsCalendar/DailyNavigation";
import WeeklyNavigation from "@components/AppointmentsCalendar/WeeklyNavigation";
import WeekSelector from "@components/AppointmentsCalendar/WeekSelector";
import MonthlyNavigation from "@components/AppointmentsCalendar/MonthlyNavigation";
import MonthSelector from "@components/AppointmentsCalendar/MonthSelector";
import Legend from "@ui/Legend";
import LegendItem from "@ui/Legend/LegendItem";
import DoctorPopup from "@components/AppointmentsCalendar/DoctorPopup";
import MobileCalendar from "@components/AppointmentsCalendar/MobileCalendar";
import Avatar from "@ui/Avatar";

// utils
import moment from "moment";
import PropTypes from "prop-types";
import { colorTypes } from "@constants/colors";
import { doctorsOptions } from "@constants/options";

// hooks
import { useState, useRef, useEffect } from "react";
import useWindowSize from "@hooks/useWindowSize";
import { useNavigate, useSearchParams } from "react-router-dom";

// data placeholder
import { events, disabled } from "@db/calendar_appointments";
import CustomSelect from "@ui/Select";
import { useSelector } from "react-redux";
import { getNameInitials } from "@utils/helpers";
import ScheduleAppointmentModal from "@components/ScheduleAppointmentModal";
import AssignShiftModal from "./AssignShiftModal";

const AppointmentsCalendar = ({ viewChangeHandler, current, type }) => {
  const width = useWindowSize().width;
  const localizer = momentLocalizer(moment);

  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { user } = useSelector((state) => state.auth);
  const { appointments } = useSelector((state) => state.appointments);
  const { caregivers, clients, nurses } = useSelector((state) => state.users);
  const findPatient = (clientId) => clients?.find((item) => item?.id === clientId);
  const findNurse = (nurseId) => {
    if (user?.role === "ADMIN") {
      return nurses?.find((item) => item?.id === nurseId);
    }
    return user;
  };

  const footerRef = useRef(null);
  const headerRef = useRef(null);
  const [height, setHeight] = useState(0);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [currentDate, setCurrentDate] = useState(moment().toDate());

  useEffect(() => {
    setHeight(headerRef.current.offsetHeight + footerRef.current.offsetHeight);
  }, [headerRef, footerRef]);

  const patientOptions = () => {
    if (!clients?.length) return [];

    return clients?.filter(item => item?.onboardPercentage === 100)
      .map((item) => ({
        ...item,
        value: item?.id,
        label: `${item?.firstName} ${item?.lastName}`,
        label2: (
          <div className="user-option">
            <Avatar
              avatar={{ jpg: item?.photo?.url }}
              alt={item?.name}
              size={40}
              initals={getNameInitials(item?.firstName, item?.lastName)}
            />
            <span>
              {item?.firstName} {item?.lastName} ({findNurse(item?.assignedNurse)?.name})
            </span>
          </div>
        ),
      }))
      .sort((a, b) => a.label.localeCompare(b.label));
  };

  // Initialize selected patient when clients data loads
  useEffect(() => {
    if (!clients?.length) return;

    const options = patientOptions();
    if (!options?.length) return;

    const patientIdFromUrl = searchParams.get("patientId");

    if (patientIdFromUrl) {
      // Try to find patient from URL parameter
      const patientFromUrl = options.find((patient) => patient.value === patientIdFromUrl);
      if (patientFromUrl) {
        setSelectedPatient(patientFromUrl);
        return;
      }
    }

    // If no URL parameter or patient not found, set to first patient
    if (!selectedPatient) {
      setSelectedPatient(options[0]);
    }
  }, [clients, searchParams]);

  // Update URL when selected patient changes (but not on initial load)
  useEffect(() => {
    if (selectedPatient?.value) {
      const currentPatientId = searchParams.get("patientId");
      if (currentPatientId !== selectedPatient.value) {
        setSearchParams({ patientId: selectedPatient.value });
      }
    }
  }, [selectedPatient]);

  // Convert appointments to calendar events directly
  const patients_appointments = selectedPatient
    ? appointments
        .filter((appointment) => appointment.client === selectedPatient.value)
        .map((appointment) => ({
          id: appointment.id,
          title: findPatient(appointment.client)?.name || "Appointment",
          start: moment(appointment.startDateTime).toDate(),
          end: moment(appointment.endDateTime).toDate(),
          allDay: false,
          type: "checkup",
          client: appointment.client,
          caregiver: appointment.caregiver,
          nurse: appointment.nurse,
          status: appointment.status,
          serviceType: appointment.serviceType || "auxiliary_nurse",
          comments: appointment.comments,
          recurrence: appointment.recurrence,
          refID: appointment.refID,
          isRecurringInstance: appointment.isRecurringInstance || false,
          originalRecurrence: appointment.originalRecurrence,
          visits: appointment.visits || null,
          name: findPatient(appointment.client)?.name || "Appointment",
        }))
    : [];

  const enrichedAppointments = selectedPatient
    ? (() => {
        // Group appointments by date and patient
        const groupedByDateAndPatient = patients_appointments.reduce((acc, appointment) => {
          const dateKey = moment(appointment.start).format("YYYY-MM-DD");
          const patientKey = appointment.client;
          const groupKey = `${dateKey}_${patientKey}`;

          

          if (!acc[groupKey]) {
            acc[groupKey] = [];
          }
          acc[groupKey].push(appointment);
          return acc;
        }, {});

        // Process each group to show only the upcoming appointment with count
        const processedAppointments = [];

        Object.values(groupedByDateAndPatient).forEach((appointmentGroup) => {
          if (appointmentGroup.length === 1) {
            // Single appointment, show as is (no multiple appointment flags)
            processedAppointments.push(appointmentGroup[0]);
          } else {
            // Multiple appointments on same day for same patient
            // Sort by start time to find the upcoming one
            const sortedAppointments = appointmentGroup.sort((a, b) => moment(a.start).diff(moment(b.start)));

            const now = moment();
            const appointmentDate = moment(sortedAppointments[0].start).startOf("day");

            let upcomingAppointment;

            if (appointmentDate.isSame(now, "day")) {
              // Today - find the next appointment that hasn't ended yet
              upcomingAppointment =
                sortedAppointments.find((apt) => moment(apt.end).isAfter(now)) ||
                sortedAppointments[sortedAppointments.length - 1]; // fallback to last if all ended
            } else {
              // Future date - show the first appointment of the day
              upcomingAppointment = sortedAppointments[0];
            }

            // Add count information to the upcoming appointment only if there are actually multiple appointments
            const appointmentWithCount = {
              ...upcomingAppointment,
              multipleAppointments: true,
              totalAppointments: appointmentGroup.length,
              remainingCount: appointmentGroup.length - 1,
              allAppointments: sortedAppointments, // Store all appointments for potential future use
            };

            processedAppointments.push(appointmentWithCount);
          }
        });

        return processedAppointments;
      })()
    : [];

  const [popupOpen, setPopupOpen] = useState(false);
  const [isScheduleOpen, setScheduleOpen] = useState(false);

  const handlePatientChange = (patient) => {
    setSelectedPatient(patient);
    // URL will be updated by the useEffect above
  };

  const PatientSelect = () => {
    return (
      <CustomSelect
        options={patientOptions()}
        value={selectedPatient}
        variant="basic"
        changeHandler={handlePatientChange}
      />
    );
  };

  const getDayFormat = () => {
    switch (true) {
      case width < 768:
        return "D";
      case width < 1600:
        return "ddd, D";
      default:
        return "dddd D MMMM";
    }
  };

  const views = ["month", "week", "day"];
  const Navigation = () => {
    return (
      <Tabs className="tabs">
        {views.map((view) => {
          return (
            <Item key={view}>
              <Button className={current === view ? "active" : null} onClick={() => viewChangeHandler(view)}>
                {view}
              </Button>
            </Item>
          );
        })}
      </Tabs>
    );
  };

  // Calculate 3-month restriction dates (including current month)
  const today = moment().startOf("day");
  const threeMonthsFromNow = moment().add(2, "months").endOf("month");

  const handleNavigation = (action, date) => {
    switch (action) {
      case "NEXT":
        setCurrentDate(moment(currentDate).add(1, "day").toDate());
        break;
      case "PREV":
        setCurrentDate(moment(currentDate).subtract(1, "day").toDate());
        break;
      default:
        setCurrentDate(date);
    }
  };

  const handleDayClick = (date) => {
    setCurrentDate(date);
    viewChangeHandler("day");
  };

  const config = {
    as: Calendar,
    localizer: localizer,
    startAccessor: "start",
    endAccessor: "end",
    views: views,
    view: current,
    date: currentDate,
    onView: viewChangeHandler,
    onNavigate: handleNavigation,
    onDrillDown: (date) => handleDayClick(date),
    events: type === "caregiver" ? (current === "month" ? enrichedAppointments : patients_appointments) : [],
    backgroundEvents: type === "caregiver" ? [] : [],
    min: moment().startOf("year").set({ hour: 6, minute: 0 }).toDate(),
    max: moment().endOf("year").set({ hour: 22, minute: 30 }).toDate(),
    timeslots: 1,
    step: 30,
    formats: {
      dayHeaderFormat: width < 414 ? "dddd, MMM DD" : "dddd, MMMM DD",
      dayFormat: getDayFormat(),
      timeGutterFormat: "HH:mm",
    },
    components: {
      toolbar: ({ label, date }) => (
        <Header ref={headerRef} view={current}>
          {current === "day" && <DailyToolbar label={false ? label : "Daily appointments scheduler"} />}
          <Navigation />
          {current === "day" && <DailyNavigation onNavigate={handleNavigation} date={date} label={label} />}
          {current === "week" && (
            <>
              {false ? <WeekSelector date={date} setter={setCurrentDate} /> : <PatientSelect />}
              <WeeklyNavigation date={date} setter={setCurrentDate} />
            </>
          )}
          {current === "month" && (
            <>
              {false ? <MonthSelector date={date} setter={setCurrentDate} /> : <PatientSelect />}
              <MonthlyNavigation date={date} setter={setCurrentDate} />
            </>
          )}
        </Header>
      ),
      event: ({ event }) => <Event event={event} variant={current} onaddAppointment={(eventData) => {
        console.log("Add appointment clicked for event:", eventData);

        // Set the current date to the event's date when adding appointment
        setCurrentDate(eventData.start);
        setPopupOpen(true);
      }}  />,
      timeSlotWrapper: (props) => TimeSlot(props, 60, 1, true),
    },
    className: `calendar-${current} calendar-${type}`,
    messages: {
      showMore: (total) => `+ ${total}`,
    },
    popup: true,
    selectable: type === "caregiver",
    onSelectEvent: (event) => {
      console.log("onSelectEvent clicked:");

      // For recurring events, navigate to the original appointment
      // For non-recurring events, use the regular id
      // const appointmentId = event?.isRecurringInstance ? event?.originalAppointmentId || event?.id : event?.id;

      // // Include selected patient ID in navigation to preserve selection when returning
      // const currentPatientId = selectedPatient?.value;
      // if (currentPatientId) {
      //   navigate(`/appointments/${appointmentId}?returnPatientId=${currentPatientId}`);
      // } else {
      //   navigate(`/appointments/${appointmentId}`);
      // }
    },
    onSelectSlot: ((slotInfo) => {
        const now = moment().startOf("day");
        const selected = moment(slotInfo.start).startOf("day");

        // Prevent creating appointments in the past
        if (selected.isBefore(now)) {
          return;
        }

        // Prevent creating appointments beyond 3 months from now (silently)
        if (selected.isAfter(threeMonthsFromNow)) {
          return;
        }

        setCurrentDate(selected);
        // setScheduleOpen(true);
        setPopupOpen(true);
      }),
    slotPropGetter: (date) => {
      const now = moment().startOf("day");
      const current = moment(date).startOf("day");
      const threeMonthsFromNow = moment().add(2, "months").endOf("month");

      // Disable past dates and dates beyond 3 months
      if (current.isBefore(now) || current.isAfter(threeMonthsFromNow)) {
        return {
          className: "rbc-slot-disabled",
          style: {
            pointerEvents: "none",
            backgroundColor: "transparent",
          },
        };
      }

      return {};
    },
    dayPropGetter: (date) => {
      const now = moment().startOf("day");
      const current = moment(date).startOf("day");
      const threeMonthsFromNow = moment().add(2, "months").endOf("month");

      // Disable past dates for appointment creation
      if (current.isBefore(now)) {
        return {
          className: "rbc-day-disabled",
        };
      }

      // Add class for dates beyond 3 months to hide plus icon
      if (current.isAfter(threeMonthsFromNow)) {
        return {
          className: "rbc-day-out-of-range",
        };
      }

      return {};
    },
  };

  // Handle mobile calendar date click
  const handleMobileDateClick = (date) => {
    const now = moment().startOf("day");
    const selected = moment(date).startOf("day");

    // Prevent creating appointments in the past
    if (selected.isBefore(now)) {
      return;
    }

    // Prevent creating appointments beyond 3 months from now (silently)
    if (selected.isAfter(threeMonthsFromNow)) {
      return;
    }

    setCurrentDate(date);
    setPopupOpen(true);
  };

  return (
    <>
      <Container>
        {/* Show mobile calendar on small screens, regular calendar on larger screens */}
        {width < 768 && type === "caregiver" ? (
          <>
            {/* Mobile header matching React Big Calendar structure */}
            <Header ref={headerRef} view={current}>
              <Navigation />
              {current === "month" && (
                <>
                  <PatientSelect />
                  <MonthlyNavigation date={currentDate} setter={setCurrentDate} />
                </>
              )}
              {current === "week" && (
                <>
                  <PatientSelect />
                  <WeeklyNavigation date={currentDate} setter={setCurrentDate} />
                </>
              )}
              {current === "day" && (
                <>
                  <DailyToolbar label="Daily appointments scheduler" />
                  <DailyNavigation onNavigate={handleNavigation} date={currentDate} label={moment(currentDate).format('dddd, MMMM DD')} />
                </>
              )}
            </Header>
            {selectedPatient ? (
              current === "month" ? (
                <MobileCalendar
                  currentDate={currentDate}
                  onDateClick={handleMobileDateClick}
                  appointments={patients_appointments}
                  selectedPatient={selectedPatient}
                  minDate={today.toDate()}
                  maxDate={threeMonthsFromNow.toDate()}
                  viewType="month"
                />
              ) : current === "week" ? (
                <MobileCalendar
                  currentDate={currentDate}
                  onDateClick={handleMobileDateClick}
                  appointments={patients_appointments}
                  selectedPatient={selectedPatient}
                  minDate={today.toDate()}
                  maxDate={threeMonthsFromNow.toDate()}
                  viewType="week"
                />
              ) : (
                <MobileCalendar
                  currentDate={currentDate}
                  onDateClick={handleMobileDateClick}
                  appointments={patients_appointments}
                  selectedPatient={selectedPatient}
                  minDate={today.toDate()}
                  maxDate={threeMonthsFromNow.toDate()}
                  viewType="day"
                />
              )
            ) : (
              <EmptyState>Please select a patient to view appointments</EmptyState>
            )}
          </>
        ) : (
          <StyledCalendar {...config} />
        )}

        {!selectedPatient && type === "caregiver" && width >= 768 && (
          <EmptyState>Please select a patient to view appointments</EmptyState>
        )}

        {type === "caregiver" && (
          <AssignShiftModal
            elemsHeight={height}
            mode="caregiver_calendar"
            name={selectedPatient?.label2 || null}
            open={popupOpen}
            handler={setPopupOpen}
            date={currentDate}
            defaultValues={{ client: selectedPatient?.value }}
            commonNurse={nurses.find((item) => item?.id === selectedPatient?.assignedNurse)}
          />
        )}
        {/* {type === "caregiver" && (
          <ScheduleAppointmentModal
            caregiver_options={caregiver_options}
            mode="caregiver_calendar"
            isVisible={isScheduleOpen}
            onCloseModal={() => setScheduleOpen(false)}
            showCloseBtn
            defaultValues={{ caregiver: selectedPatient?.id, date: moment(currentDate).format("YYYY-MM-DD") }}
            commonNurse={selectedPatient?.nurse}
            client_options={client_options}
          />
        )} */}
      </Container>
      <Footer ref={footerRef}>
        <Legend>
          {colorTypes.map(({ cat, color, label }) => {
            return <LegendItem key={cat} color={color} legend={label} />;
          })}
        </Legend>
      </Footer>
    </>
  );
};

AppointmentsCalendar.propTypes = {
  type: PropTypes.oneOf(["caregiver", "patient"]).isRequired,
};

export default AppointmentsCalendar;
